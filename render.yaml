services:
  - type: web
    name: video-transcription-service
    env: docker
    plan: free
    dockerfilePath: ./Dockerfile
    envVars:
      - key: PORT
        value: 8000
      - key: PYTHON_VERSION
        value: 3.11
      - key: WHISPER_MODEL
        value: tiny
      - key: MODEL_PRELOAD
        value: true
      - key: DEBUG
        value: false
    healthCheckPath: /health
    autoDeploy: true
